<?php
/**
 * Templates Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Set up page header variables
$page_title = __('Invoice Type', 'wp-invoice-manager-pro');
$page_subtitle = __('Create and manage your invoice types', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-admin-page';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Invoice Type', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary si-add-template-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>



    <!-- Templates Content -->

        <?php if (!empty($templates)): ?>
            <!-- Templates Grid -->
            <div class="si-templates-grid">
                <?php foreach ($templates as $template): ?>
                    <div class="si-template-card" data-template-id="<?php echo esc_attr($template->id); ?>">
                        <div class="si-template-header">
                            <div class="si-template-preview">
                                <?php
                                $design = isset($available_designs[$template->design]) ? $available_designs[$template->design] : null;
                                if ($design && !empty($design['preview_url'])):
                                ?>
                                    <img src="<?php echo esc_url($design['preview_url']); ?>" alt="<?php echo esc_attr($template->name); ?>" />
                                <?php else: ?>
                                    <div class="si-template-placeholder">
                                        <span class="dashicons dashicons-media-text"></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="si-template-badge">
                                <span class="si-badge si-badge-design"><?php echo esc_html($design ? $design['name'] : 'Classic'); ?></span>
                            </div>
                        </div>

                        <div class="si-template-body">
                            <h3 class="si-template-title"><?php echo esc_html($template->name); ?></h3>
                            <div class="si-template-meta">
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-calendar-alt"></span>
                                    <?php echo esc_html(date('M j, Y', strtotime($template->created_at))); ?>
                                </span>
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                    <?php echo esc_html($design ? $design['name'] : 'Classic'); ?>
                                </span>
                            </div>
                        </div>

                        <div class="si-template-footer">
                            <div class="si-template-actions">
                                <button type="button" class="si-btn si-btn-primary si-btn-sm si-use-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php echo esc_html__('Use', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-secondary si-btn-sm si-edit-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-danger si-btn-sm si-delete-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="si-empty-state">
                <div class="si-empty-content">
                    <div class="si-empty-icon">
                        <span class="dashicons dashicons-admin-page"></span>
                    </div>
                    <h3 class="si-empty-title"><?php echo esc_html__('No Invoice Types Yet', 'simple-invoice'); ?></h3>
                    <p class="si-empty-description">
                        <?php echo esc_html__('Create your first invoice type to get started. Invoice types help you maintain consistent branding and save time when creating invoices.', 'simple-invoice'); ?>
                        <br><br>
                        <strong><?php echo esc_html__('Example:', 'simple-invoice'); ?></strong> <?php echo esc_html__('Standard Invoice, Service Invoice, Product Invoice, Quotation, etc.', 'simple-invoice'); ?>
                    </p>
                    <div class="si-empty-actions">
                        <a href="#" class="si-btn si-btn-primary si-add-template-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Create Your First Invoice Type', 'simple-invoice'); ?>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Simple Add/Edit Invoice Type Modal -->
<div id="si-template-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-template-modal-title"><?php echo esc_html__('Add New Invoice Type', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>

        <div class="si-modal-body">
            <form id="si-template-form">
                <input type="hidden" id="si-template-id" name="template_id" value="" />

                <!-- Basic Information -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Template Information', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Create your invoice template with custom settings', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid si-grid-2">
                        <div class="si-form-field">
                            <label class="si-field-label" for="si-template-name">
                                <?php echo esc_html__('Template Name', 'simple-invoice'); ?>
                                <span class="required">*</span>
                            </label>
                            <input type="text"
                                   id="si-template-name"
                                   name="name"
                                   class="si-input"
                                   required
                                   placeholder="<?php echo esc_attr__('e.g., Standard Invoice, Service Invoice', 'simple-invoice'); ?>" />
                            <span class="si-field-help"><?php echo esc_html__('Choose a descriptive name for your template', 'simple-invoice'); ?></span>
                        </div>

                        <div class="si-form-field">
                            <label class="si-field-label" for="si-template-design">
                                <?php echo esc_html__('Design Layout', 'simple-invoice'); ?>
                            </label>
                            <select id="si-template-design" name="design" class="si-select">
                                <?php foreach ($available_designs as $design_id => $design): ?>
                                    <option value="<?php echo esc_attr($design_id); ?>" <?php selected($design_id, 'classic'); ?>>
                                        <?php echo esc_html($design['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="si-field-help"><?php echo esc_html__('Select the visual design for your invoices', 'simple-invoice'); ?></span>
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label class="si-field-label" for="si-template-description">
                            <?php echo esc_html__('Description', 'simple-invoice'); ?>
                        </label>
                        <textarea id="si-template-description"
                                  name="description"
                                  class="si-textarea"
                                  rows="3"
                                  placeholder="<?php echo esc_attr__('Brief description of when to use this template...', 'simple-invoice'); ?>"></textarea>
                        <span class="si-field-help"><?php echo esc_html__('Optional description to help identify this template', 'simple-invoice'); ?></span>
                    </div>
                </div>



                <!-- Header Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Header Information', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Select business information to display in the invoice header', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-checkbox-grid">
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_name]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-building"></span>
                                <?php echo esc_html__('Business Name', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_logo]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-format-image"></span>
                                <?php echo esc_html__('Business Logo', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_address]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-location"></span>
                                <?php echo esc_html__('Business Address', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_email]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-email"></span>
                                <?php echo esc_html__('Business Email', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_phone]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-phone"></span>
                                <?php echo esc_html__('Business Phone', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[gstin]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-id"></span>
                                <?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?>
                            </span>
                        </label>
                    </div>
                </div>
                <!-- Table Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Invoice Table Columns', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Choose which columns to include in your invoice items table', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-checkbox-grid">
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[description]" value="1" checked disabled />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-text"></span>
                                <?php echo esc_html__('Description', 'simple-invoice'); ?>
                                <small>(<?php echo esc_html__('Required', 'simple-invoice'); ?>)</small>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[quantity]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-plus-alt2"></span>
                                <?php echo esc_html__('Quantity', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[rate]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-money-alt"></span>
                                <?php echo esc_html__('Rate/Price', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[total]" value="1" checked disabled />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-calculator"></span>
                                <?php echo esc_html__('Total', 'simple-invoice'); ?>
                                <small>(<?php echo esc_html__('Required', 'simple-invoice'); ?>)</small>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[tax]" value="1" />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-chart-pie"></span>
                                <?php echo esc_html__('Tax/GST', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="table_fields[discount]" value="1" />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-tag"></span>
                                <?php echo esc_html__('Discount', 'simple-invoice'); ?>
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Summary Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Summary Section', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Configure the totals and summary information', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-checkbox-grid">
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[subtotal]" value="1" checked disabled />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-calculator"></span>
                                <?php echo esc_html__('Subtotal', 'simple-invoice'); ?>
                                <small>(<?php echo esc_html__('Required', 'simple-invoice'); ?>)</small>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[tax_total]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-chart-pie"></span>
                                <?php echo esc_html__('Tax Total', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[discount_total]" value="1" />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-tag"></span>
                                <?php echo esc_html__('Discount Total', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[grand_total]" value="1" checked disabled />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-money-alt"></span>
                                <?php echo esc_html__('Grand Total', 'simple-invoice'); ?>
                                <small>(<?php echo esc_html__('Required', 'simple-invoice'); ?>)</small>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[notes]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-edit"></span>
                                <?php echo esc_html__('Notes Section', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="summary_fields[terms]" value="1" />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-media-document"></span>
                                <?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?>
                            </span>
                        </label>
                    </div>
                </div>

            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-save-template"><?php echo esc_html__('Save Invoice Type', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<style>
/* Templates Page - Use Common Design System */

/* Templates Grid - Essential styles only */
.si-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.si-template-card {
    background: var(--background-color);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.si-template-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.si-template-header {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.si-template-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-alt-color);
}

.si-template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.si-template-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.si-template-placeholder .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
}

.si-template-body {
    padding: 20px;
}

.si-template-title {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-main-color);
}

.si-template-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--light-border-color);
    background: var(--background-alt-color);
}

.si-template-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

/* Enhanced Empty State */
.si-empty-state {
    text-align: center;
    padding: 60px 40px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--light-border-color);
    margin: 40px 0;
}

.si-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px auto;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-empty-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: var(--background-color);
}

.si-empty-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-main-color);
}

.si-empty-description {
    margin: 0 0 32px 0;
    font-size: 16px;
    color: var(--text-muted);
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.si-empty-description strong {
    color: var(--primary-color);
    font-weight: 600;
}

.si-empty-actions .si-btn {
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--background-color) !important;
    text-decoration: none !important;
    border: none;
}

.si-empty-actions .si-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 122, 69, 0.3);
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-color) 100%);
    color: var(--background-color) !important;
    text-decoration: none !important;
}

</style>







<script>
jQuery(document).ready(function($) {

    var templateModal = $('#si-template-modal');
    var templateForm = $('#si-template-form');
    var isEditing = false;


    
    // Default body fields
    var defaultBodyFields = [
        { label: 'Sr No.', name: 'sr_no', type: 'serial', required: true, editable: false },
        { label: 'Product/Service', name: 'product', type: 'text', required: true, editable: true },
        { label: 'Quantity', name: 'quantity', type: 'number', required: true, editable: true },
        { label: 'Rate', name: 'rate', type: 'currency', required: true, editable: true },
        { label: 'Total', name: 'total', type: 'calculated', formula: 'quantity * rate', required: true, editable: false }
    ];

    // Default summary fields
    var defaultSummaryFields = [
        { label: 'Subtotal', name: 'subtotal', type: 'calculated', formula: 'sum_total', required: true },
        { label: 'Tax (%)', name: 'tax', type: 'percentage', required: false },
        { label: 'Discount', name: 'discount', type: 'currency', required: false },
        { label: 'Shipping', name: 'shipping', type: 'currency', required: false },
        { label: 'Total Amount', name: 'total_amount', type: 'calculated', formula: 'subtotal + tax - discount + shipping', required: true }
    ];
    


    // Open add template modal
    $('.si-add-template-btn').on('click', function(e) {
        e.preventDefault();
        openTemplateModal();
    });

    // Open edit template modal
    $(document).on('click', '.si-edit-template', function() {
        var templateId = $(this).data('template-id');
        openTemplateModal(templateId);
    });

    // Use template for creating invoice
    $(document).on('click', '.si-use-template', function() {
        var templateId = $(this).data('template-id');
        window.location.href = '<?php echo admin_url('admin.php?page=wimp-create-invoice'); ?>&template_id=' + templateId;
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateCard = $(this).closest('.si-template-card');
        var templateName = templateCard.find('.si-template-title').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete the template', 'simple-invoice')); ?> "' + templateName + '"? <?php echo esc_js(__('This action cannot be undone.', 'simple-invoice')); ?>')) {
            deleteTemplate(templateId, templateCard);
        }
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateName = $(this).closest('.si-template-card').find('h3').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete', 'simple-invoice')); ?> "' + templateName + '"?')) {
            deleteTemplate(templateId);
        }
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeTemplateModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal-large', function(e) {
        if (e.target === this) {
            closeTemplateModal();
        }
    });

    // Prevent modal close when clicking inside content
    $('.si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });

    // Save template
    $('#si-save-template').on('click', function() {
        saveTemplate();
    });
    
    function openTemplateModal(templateId) {
        isEditing = !!templateId;

        if (isEditing) {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Edit Invoice Type', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Update Invoice Type', 'simple-invoice')); ?>');
            loadTemplateData(templateId);
        } else {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Add New Invoice Type', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Save Invoice Type', 'simple-invoice')); ?>');
            templateForm[0].reset();
            $('#si-template-id').val('');
        }

        templateModal.show();
    }

    function closeTemplateModal() {
        templateModal.hide();
        templateForm[0].reset();
        isEditing = false;
    }

    function loadTemplateData(templateId) {

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_get_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data) {
                    var template = response.data;

                    // Fill basic information
                    $('#si-template-name').val(template.name || '');
                    $('#si-template-id').val(template.id || '');

                    // Set design selection
                    if (template.design) {
                        $('input[name="design"][value="' + template.design + '"]').prop('checked', true);
                    }

                    // Set header fields
                    if (template.header_fields) {
                        var headerFields = typeof template.header_fields === 'string' ?
                            JSON.parse(template.header_fields) : template.header_fields;

                        $.each(headerFields, function(field, value) {
                            $('input[name="header_fields[' + field + ']"]').prop('checked', !!value);
                        });
                    }

                    // Clear existing fields
                    $('#si-body-fields .si-field-row').remove();
                    $('#si-summary-fields .si-field-row').remove();

                    // Load body fields
                    if (template.body_fields) {
                        var bodyFields = typeof template.body_fields === 'string' ?
                            JSON.parse(template.body_fields) : template.body_fields;

                        if (Array.isArray(bodyFields)) {
                            bodyFields.forEach(function(field, index) {
                                addBodyField(field, index);
                            });
                        }
                    }

                    // Load summary fields
                    if (template.summary_fields) {
                        var summaryFields = typeof template.summary_fields === 'string' ?
                            JSON.parse(template.summary_fields) : template.summary_fields;

                        if (Array.isArray(summaryFields)) {
                            summaryFields.forEach(function(field, index) {
                                addSummaryField(field, index);
                            });
                        }
                    }

                    // Update available fields list
                    updateAvailableFieldsList();
                    toggleEmptyState();

                } else {
                    alert('<?php echo esc_js(__('Failed to load template data. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('Error loading template data. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function deleteTemplate(templateId, templateCard) {

        // Show loading state
        templateCard.css('opacity', '0.5');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Remove template card with animation
                    templateCard.fadeOut(300, function() {
                        $(this).remove();

                        // Check if no templates left
                        if ($('.si-template-card').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });

                    // Show success message (you can implement a toast notification here)
                    alert('<?php echo esc_js(__('Template deleted successfully.', 'simple-invoice')); ?>');

                } else {
                    templateCard.css('opacity', '1');
                    alert(response.data || '<?php echo esc_js(__('Failed to delete template. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                templateCard.css('opacity', '1');
                alert('<?php echo esc_js(__('Error deleting template. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function loadDefaultFields() {
        // Load default body fields
        var bodyFieldsContainer = $('#si-body-fields');
        bodyFieldsContainer.empty();
        
        defaultBodyFields.forEach(function(field, index) {
            addBodyField(field, index);
        });
        
        // Load default summary fields
        var summaryFieldsContainer = $('#si-summary-fields');
        summaryFieldsContainer.empty();
        
        defaultSummaryFields.forEach(function(field, index) {
            addSummaryField(field, index);
        });

        toggleEmptyState();

        // Ensure all formula fields are properly shown/hidden and remove buttons work
        setTimeout(function() {
            $('.si-field-type-select').each(function() {
                var fieldRow = $(this).closest('.si-field-row');
                var formulaField = fieldRow.find('.si-formula-field');

                if ($(this).val() === 'calculated') {
                    formulaField.show();
                } else {
                    formulaField.hide();
                }
            });

            // Ensure all remove buttons work
            $('.si-remove-field').each(function() {
                var button = $(this);
                button.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).closest('.si-field-row').remove();
                    updateAvailableFieldsList();
                    toggleEmptyState();
                });
            });
        }, 100);
    }
    
    function addBodyField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="body_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD, Product Name" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="body_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd, product_name" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="body_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="text"' + (field.type === 'text' ? ' selected' : '') + '>Text</option>' +
                    '<option value="number"' + (field.type === 'number' ? ' selected' : '') + '>Number</option>' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                    '<option value="serial"' + (field.type === 'serial' ? ' selected' : '') + '>Serial Number</option>' +
                '</select>' +
                '<label><input type="checkbox" name="body_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="body_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., quantity * rate, abcd + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">quantity, rate, subtotal</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-body-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-body-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    function addSummaryField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="summary_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD Total, Final Amount" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="summary_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd_total, final_amount" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="summary_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="percentage"' + (field.type === 'percentage' ? ' selected' : '') + '>Percentage</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                '</select>' +
                '<label><input type="checkbox" name="summary_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="summary_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., subtotal + tax - discount, abcd_total + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">subtotal, tax, discount, shipping</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-summary-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-summary-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    // Add new body field
    $('#si-add-body-field').on('click', function() {
        var index = $('#si-body-fields .si-field-row').length;
        addBodyField({}, index);
    });
    
    // Add new summary field
    $('#si-add-summary-field').on('click', function() {
        var index = $('#si-summary-fields .si-field-row').length;
        addSummaryField({}, index);
    });
    
    // Remove field
    $(document).on('click', '.si-remove-field', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var fieldRow = $(this).closest('.si-field-row');

        if (fieldRow.length > 0) {
            fieldRow.remove();

            updateAvailableFieldsList();
            toggleEmptyState();
        }
    });

    // Show/hide formula field based on field type
    $(document).on('change', '.si-field-type-select', function() {
        var fieldRow = $(this).closest('.si-field-row');
        var formulaField = fieldRow.find('.si-formula-field');
        var selectedValue = $(this).val();

        if (selectedValue === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }
    });

    // Update available fields list when field name changes
    $(document).on('input', '.si-field-name', function() {
        updateAvailableFieldsList();
    });

    // Additional remove button handler (backup)
    $(document).on('mousedown', '.si-remove-field', function(e) {
        e.preventDefault();
    });

    // Touch support for mobile
    $(document).on('touchstart', '.si-remove-field', function(e) {
        e.preventDefault();
        $(this).trigger('click');
    });

    // Validate formula on input
    $(document).on('input', '.si-formula-field input[type="text"]', function() {
        validateFormula($(this));
    });

    // Auto-generate field name from label
    $(document).on('input', '.si-field-label', function() {
        var nameField = $(this).closest('.si-field-inputs').find('.si-field-name');
        if (!nameField.val()) {
            var generatedName = $(this).val().toLowerCase()
                .replace(/[^a-z\s]/g, '')     // Only letters and spaces
                .replace(/\s+/g, '_')         // Replace spaces with underscore
                .replace(/^_+/, '')           // Remove leading underscores
                .replace(/_+/g, '_')          // Replace multiple underscores with single
                .substring(0, 25);            // Limit length
            nameField.val(generatedName);
            updateAvailableFieldsList();
        }
    });

    // Validate field name input - only lowercase letters and underscore
    $(document).on('input', '.si-field-name', function() {
        var input = $(this);
        var value = input.val();
        var cleanValue = value.toLowerCase()
            .replace(/[^a-z_]/g, '')  // Only allow lowercase letters and underscore
            .replace(/^_+/, '')       // Remove leading underscores
            .replace(/_+/g, '_')      // Replace multiple underscores with single
            .substring(0, 30);        // Limit length

        if (value !== cleanValue) {
            input.val(cleanValue);

            // Show validation message
            var existingMsg = input.siblings('.si-field-name-error');
            existingMsg.remove();

            if (value !== cleanValue) {
                input.after('<div class="si-field-name-error">Only lowercase letters and underscore allowed</div>');
                setTimeout(function() {
                    input.siblings('.si-field-name-error').fadeOut(function() {
                        $(this).remove();
                    });
                }, 2000);
            }
        }

        updateAvailableFieldsList();
    });

    // Prevent invalid characters on keypress
    $(document).on('keypress', '.si-field-name', function(e) {
        var char = String.fromCharCode(e.which);
        var allowedChars = /[a-z_]/;

        if (!allowedChars.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
            return false;
        }
    });

    // Prevent paste of invalid content
    $(document).on('paste', '.si-field-name', function(e) {
        var input = $(this);
        setTimeout(function() {
            var value = input.val();
            var cleanValue = value.toLowerCase()
                .replace(/[^a-z_]/g, '')
                .replace(/^_+/, '')
                .replace(/_+/g, '_')
                .substring(0, 30);
            input.val(cleanValue);
            updateAvailableFieldsList();
        }, 1);
    });

    // Function to update available fields list in formula descriptions
    function updateAvailableFieldsList() {
        var bodyFieldNames = [];
        var summaryFieldNames = [];

        // Collect body field names
        $('#si-body-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                bodyFieldNames.push(name);
            }
        });

        // Collect summary field names
        $('#si-summary-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                summaryFieldNames.push(name);
            }
        });

        // Add default field names
        var defaultBodyFields = ['quantity', 'rate', 'subtotal'];
        var defaultSummaryFields = ['subtotal', 'tax', 'discount', 'shipping'];

        var allBodyFields = defaultBodyFields.concat(bodyFieldNames);
        var allSummaryFields = defaultSummaryFields.concat(summaryFieldNames).concat(bodyFieldNames);

        // Update body fields available list
        $('#si-body-fields .si-field-names-list').text(allBodyFields.join(', '));

        // Update summary fields available list
        $('#si-summary-fields .si-field-names-list').text(allSummaryFields.join(', '));
    }

    // Formula validation function
    function validateFormula(formulaInput) {
        var formula = formulaInput.val().trim();
        var errorContainer = formulaInput.siblings('.si-formula-error');
        var successContainer = formulaInput.siblings('.si-formula-success');

        // Remove existing validation messages
        errorContainer.remove();
        successContainer.remove();

        if (!formula) {
            return;
        }

        // Get available field names
        var availableFields = [];
        formulaInput.closest('.si-field-row').parent().find('.si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                availableFields.push(name);
            }
        });

        // Add default fields
        if (formulaInput.closest('#si-body-fields').length) {
            availableFields = availableFields.concat(['quantity', 'rate', 'subtotal']);
        } else {
            availableFields = availableFields.concat(['subtotal', 'tax', 'discount', 'shipping']);
        }

        // Basic formula validation
        var isValid = true;
        var errorMessage = '';

        // Check for basic syntax
        if (!/^[a-zA-Z0-9_\s\+\-\*\/\(\)\.]+$/.test(formula)) {
            isValid = false;
            errorMessage = 'Formula contains invalid characters. Use only letters, numbers, +, -, *, /, (, )';
        }

        // Check for field names
        var fieldNames = formula.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
        for (var i = 0; i < fieldNames.length; i++) {
            if (availableFields.indexOf(fieldNames[i]) === -1 &&
                !['sum_total'].includes(fieldNames[i])) {
                isValid = false;
                errorMessage = 'Unknown field: "' + fieldNames[i] + '". Available fields: ' + availableFields.join(', ');
                break;
            }
        }

        // Show validation result
        if (isValid) {
            formulaInput.after('<div class="si-formula-success">✓ Formula looks good!</div>');
        } else {
            formulaInput.after('<div class="si-formula-error">✗ ' + errorMessage + '</div>');
        }
    }

    // Toggle empty state visibility
    function toggleEmptyState() {
        // Body fields empty state
        var bodyFieldsCount = $('#si-body-fields .si-field-row').length;
        if (bodyFieldsCount === 0) {
            $('#si-body-fields-empty').show();
        } else {
            $('#si-body-fields-empty').hide();
        }

        // Summary fields empty state
        var summaryFieldsCount = $('#si-summary-fields .si-field-row').length;
        if (summaryFieldsCount === 0) {
            $('#si-summary-fields-empty').show();
        } else {
            $('#si-summary-fields-empty').hide();
        }
    }






    
    function saveTemplate() {
        var formData = templateForm.serialize();
        var action = isEditing ? 'si_edit_template' : 'si_add_template';
        var nonce = isEditing ? '<?php echo wp_create_nonce('si_edit_template_nonce'); ?>' : '<?php echo wp_create_nonce('si_add_template_nonce'); ?>';
        
        formData += '&action=' + action + '&nonce=' + nonce;
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    closeTemplateModal();
                    location.reload(); // Refresh the page to show updated data
                } else {
                    alert(response.message || '<?php echo esc_js(__('An error occurred.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function deleteTemplate(templateId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('.si-template-card[data-template-id="' + templateId + '"]').fadeOut(function() {
                        $(this).remove();
                        
                        // Check if grid is empty
                        if ($('.si-template-card').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to delete template.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    // Initialize default fields on page load
    loadDefaultFields();

    // Design Guide Modal
    $('.si-view-guide').on('click', function(e) {
        e.preventDefault();
        $('#si-design-guide-modal').fadeIn(300);
    });

    // Close design guide modal
    $('#si-design-guide-modal .si-modal-close, #si-design-guide-modal').on('click', function(e) {
        if (e.target === this) {
            $('#si-design-guide-modal').fadeOut(300);
        }
    });

    // Prevent modal content clicks from closing modal
    $('#si-design-guide-modal .si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });
});
</script>

<!-- Design Guide Modal -->
<div id="si-design-guide-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content si-modal-large">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Custom Design Guide', 'simple-invoice'); ?></h2>
            <span class="si-modal-close">&times;</span>
        </div>
        <div class="si-modal-body">
            <div class="si-guide-content">
                <h3><?php echo esc_html__('How to Add Custom Designs', 'simple-invoice'); ?></h3>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">1</span> <?php echo esc_html__('Create Design Folder', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Create a new folder in:', 'simple-invoice'); ?></p>
                    <code>wp-content/plugins/simple-invoice/designs/your-design-name/</code>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">2</span> <?php echo esc_html__('Required Files', 'simple-invoice'); ?></h4>
                    <ul>
                        <li><strong>template.php</strong> - <?php echo esc_html__('Main template file (required)', 'simple-invoice'); ?></li>
                        <li><strong>style.css</strong> - <?php echo esc_html__('Custom CSS styles (optional)', 'simple-invoice'); ?></li>
                        <li><strong>preview.jpg</strong> - <?php echo esc_html__('Preview image (optional)', 'simple-invoice'); ?></li>
                    </ul>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">3</span> <?php echo esc_html__('Available Variables', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Use these variables in your template:', 'simple-invoice'); ?></p>
                    <div class="si-variables-grid">
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Business Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$settings['business_name']</code></li>
                                <li><code>$settings['business_address']</code></li>
                                <li><code>$settings['business_email']</code></li>
                                <li><code>$settings['business_phone']</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$invoice_number</code></li>
                                <li><code>$invoice_date</code></li>
                                <li><code>$due_date</code></li>
                                <li><code>$notes</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Client Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$client->name</code></li>
                                <li><code>$client->email</code></li>
                                <li><code>$client->address</code></li>
                                <li><code>$client->contact_number</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Calculations', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$items</code> - <?php echo esc_html__('Array of items', 'simple-invoice'); ?></li>
                                <li><code>$subtotal</code></li>
                                <li><code>$tax_amount</code></li>
                                <li><code>$total_amount</code></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">4</span> <?php echo esc_html__('Quick Start', 'simple-invoice'); ?></h4>
                    <ol>
                        <li><?php echo esc_html__('Copy an existing design folder (e.g., "classic")', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Rename it to your design name', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Modify template.php with your custom HTML/CSS', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Refresh this page to see your new design', 'simple-invoice'); ?></li>
                    </ol>
                </div>

                <div class="si-guide-actions">
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'DESIGN-TEMPLATES-GUIDE.md'); ?>" target="_blank" class="si-btn si-btn-primary">
                        <span class="dashicons dashicons-external"></span>
                        <?php echo esc_html__('View Full Documentation', 'simple-invoice'); ?>
                    </a>
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'designs/classic/template.php'); ?>" target="_blank" class="si-btn si-btn-secondary">
                        <span class="dashicons dashicons-media-code"></span>
                        <?php echo esc_html__('View Example Template', 'simple-invoice'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
