<?php
/**
 * Templates Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Set up page header variables
$page_title = __('Invoice Type', 'wp-invoice-manager-pro');
$page_subtitle = __('Create and manage your invoice types', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-admin-page';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Invoice Type', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary si-add-template-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>



    <!-- Templates Content -->

        <?php if (!empty($templates)): ?>
            <!-- Templates Grid -->
            <div class="si-templates-grid">
                <?php foreach ($templates as $template): ?>
                    <div class="si-template-card" data-template-id="<?php echo esc_attr($template->id); ?>">
                        <div class="si-template-header">
                            <div class="si-template-preview">
                                <?php
                                $design = isset($available_designs[$template->design]) ? $available_designs[$template->design] : null;
                                if ($design && !empty($design['preview_url'])):
                                ?>
                                    <img src="<?php echo esc_url($design['preview_url']); ?>" alt="<?php echo esc_attr($template->name); ?>" />
                                <?php else: ?>
                                    <div class="si-template-placeholder">
                                        <span class="dashicons dashicons-media-text"></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="si-template-badge">
                                <span class="si-badge si-badge-design"><?php echo esc_html($design ? $design['name'] : 'Classic'); ?></span>
                            </div>
                        </div>

                        <div class="si-template-body">
                            <h3 class="si-template-title"><?php echo esc_html($template->name); ?></h3>
                            <div class="si-template-meta">
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-calendar-alt"></span>
                                    <?php echo esc_html(date('M j, Y', strtotime($template->created_at))); ?>
                                </span>
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                    <?php echo esc_html($design ? $design['name'] : 'Classic'); ?>
                                </span>
                            </div>
                        </div>

                        <div class="si-template-footer">
                            <div class="si-template-actions">
                                <button type="button" class="si-btn si-btn-primary si-btn-sm si-use-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php echo esc_html__('Use', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-secondary si-btn-sm si-edit-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-danger si-btn-sm si-delete-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="si-empty-state">
                <div class="si-empty-content">
                    <div class="si-empty-icon">
                        <span class="dashicons dashicons-admin-page"></span>
                    </div>
                    <h3 class="si-empty-title"><?php echo esc_html__('No Invoice Types Yet', 'simple-invoice'); ?></h3>
                    <p class="si-empty-description">
                        <?php echo esc_html__('Create your first invoice type to get started. Invoice types help you maintain consistent branding and save time when creating invoices.', 'simple-invoice'); ?>
                        <br><br>
                        <strong><?php echo esc_html__('Example:', 'simple-invoice'); ?></strong> <?php echo esc_html__('Standard Invoice, Service Invoice, Product Invoice, Quotation, etc.', 'simple-invoice'); ?>
                    </p>
                    <div class="si-empty-actions">
                        <a href="#" class="si-btn si-btn-primary si-add-template-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Create Your First Invoice Type', 'simple-invoice'); ?>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Simple Add/Edit Invoice Type Modal -->
<div id="si-template-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-template-modal-title"><?php echo esc_html__('Add New Invoice Type', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>

        <div class="si-modal-body">
            <form id="si-template-form">
                <input type="hidden" id="si-template-id" name="template_id" value="" />

                <!-- Basic Information -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Invoice Type Information', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Create your invoice type with custom settings', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid si-grid-2">
                        <div class="si-form-field">
                            <label class="si-field-label" for="si-template-name">
                                <?php echo esc_html__('Invoice Type Name', 'simple-invoice'); ?>
                                <span class="required">*</span>
                            </label>
                            <input type="text"
                                   id="si-template-name"
                                   name="name"
                                   class="si-input"
                                   required
                                   placeholder="<?php echo esc_attr__('e.g., Standard Invoice, Service Invoice', 'simple-invoice'); ?>" />
                            <span class="si-field-help"><?php echo esc_html__('Choose a descriptive name for your invoice type', 'simple-invoice'); ?></span>
                        </div>

                        <div class="si-form-field">
                            <label class="si-field-label" for="si-template-design">
                                <?php echo esc_html__('Design Layout', 'simple-invoice'); ?>
                            </label>
                            <select id="si-template-design" name="design" class="si-select">
                                <?php foreach ($available_designs as $design_id => $design): ?>
                                    <option value="<?php echo esc_attr($design_id); ?>" <?php selected($design_id, 'classic'); ?>>
                                        <?php echo esc_html($design['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="si-field-help"><?php echo esc_html__('Select the visual design for your invoices', 'simple-invoice'); ?></span>
                        </div>
                    </div>

                    <div class="si-form-field si-form-grid">
                        <label class="si-field-label" for="si-template-description">
                            <?php echo esc_html__('Description', 'simple-invoice'); ?>
                        </label>
                        <textarea id="si-template-description"
                                  name="description"
                                  class="si-textarea"
                                  rows="3"
                                  placeholder="<?php echo esc_attr__('Brief description of when to use this invoice type...', 'simple-invoice'); ?>"></textarea>
                        <span class="si-field-help"><?php echo esc_html__('Optional description to help identify this invoice type', 'simple-invoice'); ?></span>
                    </div>
                </div>



                <!-- Header Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Header Information', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Select business information to display in the invoice header', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-checkbox-grid">
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_name]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-building"></span>
                                <?php echo esc_html__('Business Name', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_logo]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-format-image"></span>
                                <?php echo esc_html__('Business Logo', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_address]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-location"></span>
                                <?php echo esc_html__('Business Address', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_email]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-email"></span>
                                <?php echo esc_html__('Business Email', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[business_phone]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-phone"></span>
                                <?php echo esc_html__('Business Phone', 'simple-invoice'); ?>
                            </span>
                        </label>
                        <label class="si-checkbox-item">
                            <input type="checkbox" name="header_fields[gstin]" value="1" checked />
                            <span class="si-checkbox-label">
                                <span class="dashicons dashicons-id"></span>
                                <?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?>
                            </span>
                        </label>
                    </div>
                </div>
                <!-- Table Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Invoice Table Columns', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Drag to reorder columns, remove unwanted columns, or add custom columns', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-fields-table">
                        <div class="si-table-header">
                            <div class="si-col-label">Field Label</div>
                            <div class="si-col-name">Field Name</div>
                            <div class="si-col-required">Required</div>
                            <div class="si-col-type">Field Type</div>
                            <div class="si-col-action">Action</div>
                        </div>
                        <div id="si-table-columns-container" class="si-table-body">
                            <!-- Default table columns will be loaded here -->
                        </div>
                    </div>

                    <div class="si-add-field-actions">
                        <button type="button" class="button button-secondary" id="si-add-table-field">
                            <span class="dashicons dashicons-table-col-after"></span>
                            <?php echo esc_html__('Add Custom Column', 'simple-invoice'); ?>
                        </button>
                    </div>
                </div>

                <!-- Summary Fields -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3><?php echo esc_html__('Summary Section', 'simple-invoice'); ?></h3>
                        <p><?php echo esc_html__('Drag to reorder summary fields, remove unwanted fields, or add custom fields', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-fields-table">
                        <div class="si-table-header">
                            <div class="si-col-label">Field Label</div>
                            <div class="si-col-name">Field Name</div>
                            <div class="si-col-required">Required</div>
                            <div class="si-col-type">Field Type</div>
                            <div class="si-col-action">Action</div>
                        </div>
                        <div id="si-summary-fields-container" class="si-table-body">
                            <!-- Default summary fields will be loaded here -->
                        </div>
                    </div>

                    <div class="si-add-field-actions">
                        <button type="button" class="button button-secondary" id="si-add-summary-field">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Custom Summary Field', 'simple-invoice'); ?>
                        </button>
                    </div>
                </div>



            </form>
        </div>

        <div class="si-modal-footer">
            <div class="si-footer-left">
                <button type="button" class="button button-secondary si-modal-close">
                    <span class="dashicons dashicons-no-alt"></span>
                    <?php echo esc_html__('Cancel', 'simple-invoice'); ?>
                </button>
            </div>
            <div class="si-footer-right">
                <button type="button" class="button button-primary" id="si-save-template">
                    <span class="dashicons dashicons-saved"></span>
                    <?php echo esc_html__('Save Invoice Type', 'simple-invoice'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Templates Page - Use Common Design System */

/* Templates Grid - Essential styles only */
.si-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.si-template-card {
    background: var(--background-color);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.si-template-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.si-template-header {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.si-template-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-alt-color);
}

.si-template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.si-template-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.si-template-placeholder .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
}

.si-template-body {
    padding: 20px;
}

.si-template-title {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-main-color);
}

.si-template-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--light-border-color);
    background: var(--background-alt-color);
}

.si-template-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

/* Enhanced Empty State */
.si-empty-state {
    text-align: center;
    padding: 60px 40px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--light-border-color);
    margin: 40px 0;
}

.si-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px auto;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-empty-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: var(--background-color);
}

.si-empty-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-main-color);
}

.si-empty-description {
    margin: 0 0 32px 0;
    font-size: 16px;
    color: var(--text-muted);
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.si-empty-description strong {
    color: var(--primary-color);
    font-weight: 600;
}

.si-empty-actions .si-btn {
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--background-color) !important;
    text-decoration: none !important;
    border: none;
}

.si-empty-actions .si-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 122, 69, 0.3);
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-color) 100%);
    color: var(--background-color) !important;
    text-decoration: none !important;
}

/* Enhanced Modal Design */
.si-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(3px);
}

.si-modal-content {
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.si-modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: var(--background-color);
    padding: 24px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.si-modal-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.si-modal-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    color: var(--background-color);
    position: relative;
    z-index: 1;
}

.si-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--background-color);
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    line-height: 1;
    transition: var(--transition);
    position: relative;
    z-index: 1;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.si-modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
    background: var(--background-color);
}

.si-modal-body::-webkit-scrollbar {
    width: 6px;
}

.si-modal-body::-webkit-scrollbar-track {
    background: var(--background-alt-color);
}

.si-modal-body::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.si-modal-footer {
    background: linear-gradient(135deg, var(--background-alt-color) 0%, rgba(244, 122, 69, 0.02) 100%);
    border-top: 1px solid var(--light-border-color);
    padding: 24px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    position: relative;
}

.si-modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.3;
}

.si-footer-left,
.si-footer-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.si-modal-footer .button {
    padding: 14px 28px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 14px;
    min-width: 140px;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.si-modal-footer .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
}

.si-modal-footer .button-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-color: var(--primary-color);
    color: var(--background-color) !important;
    box-shadow: var(--shadow-sm);
}

.si-modal-footer .button-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-hover);
    color: var(--background-color) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none !important;
}

.si-modal-footer .button-secondary {
    background: var(--background-color);
    border: 2px solid var(--light-border-color);
    color: var(--text-main-color) !important;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.si-modal-footer .button-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(95, 95, 95, 0.1), transparent);
    transition: left 0.5s ease;
}

.si-modal-footer .button-secondary:hover {
    background: var(--background-color);
    border-color: var(--secondary-color);
    color: var(--secondary-color) !important;
    transform: translateY(-2px);
    text-decoration: none !important;
    box-shadow: var(--shadow-md);
}

.si-modal-footer .button-secondary:hover::before {
    left: 100%;
}

.si-modal-footer .button-secondary:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(95, 95, 95, 0.2);
}

.si-modal-footer .button-secondary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Form Sections */
.si-form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--light-border-color);
}

.si-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.si-section-header {
    text-align: left;
    margin-bottom: 24px;
}

.si-section-header h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-main-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.si-section-header p {
    margin: 0;
    color: var(--text-muted);
    font-size: 14px;
    line-height: 1.5;
}

/* Form Grid */
.si-form-grid {
    display: grid;
    gap: 20px;
}

.si-grid-2 {
    grid-template-columns: 1fr 1fr;
}

.si-form-field {
    display: flex;
    flex-direction: column;
}

.si-form-field.si-full-width {
    grid-column: 1 / -1;
    margin-top: 8px;
}

.si-form-field.si-full-width .si-field-label {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-main-color);
    margin-bottom: 4px;
    display: block;
}

.si-form-field.si-full-width .si-field-help {
    margin-top: 6px;
    font-size: 13px;
    color: var(--text-muted);
    line-height: 1.4;
}

.si-field-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-main-color);
    font-size: 14px;
}

.si-field-label .required {
    color: var(--primary-color);
    font-weight: 700;
}

.si-input,
.si-textarea,
.si-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-family: inherit;
    transition: var(--transition);
    background-color: var(--background-color);
    color: var(--text-main-color);
}

.si-input:focus,
.si-textarea:focus,
.si-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(244, 122, 69, 0.1);
}

.si-textarea {
    resize: vertical;
    min-height: 100px;
    max-height: 200px;
    font-family: inherit;
    line-height: 1.5;
    padding: 12px 16px;
    border: 2px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    background: var(--background-color);
    color: var(--text-main-color);
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
    box-sizing: border-box;
}

.si-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(244, 122, 69, 0.1);
    background: rgba(244, 122, 69, 0.02);
}

.si-textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
    opacity: 0.7;
}

.si-textarea:hover {
    border-color: rgba(244, 122, 69, 0.5);
}

.si-field-help {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 6px;
    line-height: 1.4;
}

/* Checkbox Grid */
.si-checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
}

.si-checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 16px;
    background: var(--background-color);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.si-checkbox-item:hover {
    border-color: var(--primary-color);
    background: rgba(244, 122, 69, 0.05);
}

.si-checkbox-item input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.si-checkbox-item input[type="checkbox"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.si-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-main-color);
    flex: 1;
    cursor: pointer;
}

.si-checkbox-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: var(--primary-color);
}

.si-checkbox-label small {
    color: var(--text-muted);
    font-weight: 400;
    font-size: 11px;
    margin-left: 4px;
}



/* Fields Table Layout */
.si-fields-table {
    background: var(--background-color);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 20px;
}

.si-table-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1.5fr 1fr;
    gap: 16px;
    padding: 16px;
    background: var(--background-alt-color);
    border-bottom: 1px solid var(--light-border-color);
    font-weight: 600;
    color: var(--text-main-color);
    font-size: 14px;
}

.si-table-body {
    min-height: 100px;
    padding: 8px;
}

.si-table-body.ui-sortable-helper {
    background: var(--background-color);
    box-shadow: var(--shadow-md);
}

/* Sortable Field Item - Table Row */
.si-sortable-field {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1.5fr 1fr;
    gap: 16px;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 4px;
    background: var(--background-color);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    cursor: move;
    transition: var(--transition);
    position: relative;
}

.si-sortable-field:hover {
    background: rgba(244, 122, 69, 0.05);
    border-color: var(--primary-color);
}

.si-sortable-field.ui-sortable-helper {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
    background: var(--background-color);
    z-index: 1000;
}

.si-sortable-field.ui-sortable-placeholder {
    background: rgba(244, 122, 69, 0.1);
    border: 2px dashed var(--primary-color);
    height: 50px;
    visibility: visible !important;
}

.si-field-label-cell {
    display: flex;
    align-items: center;
}

.si-field-label-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    font-size: 13px;
    background: var(--background-color);
    color: var(--text-main-color);
    transition: var(--transition);
}

.si-field-label-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(244, 122, 69, 0.1);
}

.si-field-name-cell {
    display: flex;
    align-items: center;
}

.si-field-name-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: var(--background-color);
    color: var(--text-main-color);
    transition: var(--transition);
}

.si-field-name-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(244, 122, 69, 0.1);
}

.si-field-required-cell {
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-required-checkbox {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.si-field-type-cell {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.si-field-type-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    font-size: 13px;
    background: var(--background-color);
    color: var(--text-main-color);
    transition: var(--transition);
}

.si-field-type-select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(244, 122, 69, 0.1);
}

.si-formula-field {
    margin-top: 8px;
    padding: 8px;
    background: rgba(244, 122, 69, 0.05);
    border: 1px solid rgba(244, 122, 69, 0.2);
    border-radius: var(--border-radius-sm);
}

.si-formula-input {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid rgba(244, 122, 69, 0.3);
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-family: 'Courier New', monospace;
    background: var(--background-color);
    margin-bottom: 4px;
}

.si-formula-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(244, 122, 69, 0.1);
}

.si-formula-field small {
    font-size: 10px;
    color: var(--text-muted);
    line-height: 1.2;
}

.si-field-action-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Add Field Actions */
.si-add-field-actions {
    padding: 16px;
    text-align: center;
    background: var(--background-alt-color);
    border-top: 1px solid var(--light-border-color);
}

.si-add-field-actions .button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    color: var(--background-color) !important;
    padding: 12px 20px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none !important;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
}

.si-add-field-actions .button:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-color) 100%);
    color: var(--background-color) !important;
    text-decoration: none !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.si-add-field-actions .button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(244, 122, 69, 0.3);
}

.si-add-field-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 4px;
}

.si-add-field-actions .button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.si-drag-handle {
    cursor: move;
    color: var(--text-muted);
    font-size: 14px;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.si-drag-handle:hover {
    color: var(--primary-color);
    background: rgba(244, 122, 69, 0.1);
}

.si-remove-field-btn {
    background: #dc3545 !important;
    border: none !important;
    color: white !important;
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 11px;
    font-weight: 600;
    cursor: pointer !important;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 4px;
    position: relative;
    z-index: 100 !important;
    pointer-events: auto !important;
    user-select: none;
}

.si-remove-field-btn:hover {
    background: #c82333;
    transform: scale(1.05);
    color: white;
}

.si-remove-field-btn:focus {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
}

.si-remove-field-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Empty State for Sortable */
.si-sortable-empty {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.si-sortable-empty .dashicons {
    font-size: 32px;
    margin-bottom: 12px;
}

.si-sortable-empty h4 {
    margin: 0 0 8px 0;
    color: var(--text-main-color);
    font-size: 16px;
}

.si-sortable-empty p {
    margin: 0;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-modal-content {
        width: 98%;
        margin: 10px;
    }

    .si-modal-header,
    .si-modal-body,
    .si-modal-footer {
        padding: 20px;
    }

    .si-grid-2 {
        grid-template-columns: 1fr;
    }

    .si-checkbox-grid {
        grid-template-columns: 1fr;
    }

    .si-modal-footer {
        flex-direction: column;
        gap: 12px;
    }

    .si-footer-left,
    .si-footer-right {
        width: 100%;
        justify-content: center;
    }

    .si-modal-footer .button {
        width: 100%;
        justify-content: center;
        min-width: auto;
        padding: 16px 20px;
    }

    .si-field-inputs {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .si-remove-field {
        position: static;
        margin-top: 12px;
        align-self: flex-start;
        width: auto;
    }

    .si-table-header,
    .si-sortable-field {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .si-table-header {
        display: none;
    }

    .si-sortable-field {
        padding: 16px;
        border: 1px solid var(--light-border-color);
        border-radius: var(--border-radius);
        margin-bottom: 12px;
    }

    .si-sortable-field > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid var(--light-border-color);
    }

    .si-sortable-field > div:last-child {
        border-bottom: none;
    }

    .si-sortable-field > div::before {
        content: attr(data-label);
        font-weight: 600;
        color: var(--text-main-color);
        font-size: 12px;
        text-transform: uppercase;
        margin-right: 8px;
    }

    .si-field-label-cell::before { content: "Label: "; }
    .si-field-name-cell::before { content: "Name: "; }
    .si-field-required-cell::before { content: "Required: "; }
    .si-field-type-cell::before { content: "Type: "; }
    .si-field-action-cell::before { content: "Action: "; }

    .si-field-label-input,
    .si-field-name-input,
    .si-field-type-select {
        font-size: 12px;
        padding: 4px 6px;
    }

    .si-formula-field {
        margin-top: 4px;
        padding: 6px;
    }

    .si-formula-input {
        font-size: 11px;
        padding: 3px 5px;
    }

    .si-add-field-actions {
        padding: 12px;
    }

    .si-add-field-actions .button {
        width: 100%;
        min-width: auto;
        padding: 14px 16px;
        font-size: 13px;
    }

    .si-add-field-actions .button .dashicons {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }
}

</style>







<script>
jQuery(document).ready(function($) {

    var templateModal = $('#si-template-modal');
    var templateForm = $('#si-template-form');
    var isEditing = false;


    
    // Default body fields
    var defaultBodyFields = [
        { label: 'Sr No.', name: 'sr_no', type: 'serial', required: true, editable: false },
        { label: 'Product/Service', name: 'product', type: 'text', required: true, editable: true },
        { label: 'Quantity', name: 'quantity', type: 'number', required: true, editable: true },
        { label: 'Rate', name: 'rate', type: 'currency', required: true, editable: true },
        { label: 'Total', name: 'total', type: 'calculated', formula: 'quantity * rate', required: true, editable: false }
    ];

    // Default summary fields
    var defaultSummaryFields = [
        { label: 'Subtotal', name: 'subtotal', type: 'calculated', formula: 'sum_total', required: true },
        { label: 'Tax (%)', name: 'tax', type: 'percentage', required: false },
        { label: 'Discount', name: 'discount', type: 'currency', required: false },
        { label: 'Shipping', name: 'shipping', type: 'currency', required: false },
        { label: 'Total Amount', name: 'total_amount', type: 'calculated', formula: 'subtotal + tax - discount + shipping', required: true }
    ];
    


    // Open add template modal
    $('.si-add-template-btn').on('click', function(e) {
        e.preventDefault();
        openTemplateModal();
    });

    // Open edit template modal
    $(document).on('click', '.si-edit-template', function() {
        var templateId = $(this).data('template-id');
        openTemplateModal(templateId);
    });

    // Use template for creating invoice
    $(document).on('click', '.si-use-template', function() {
        var templateId = $(this).data('template-id');
        window.location.href = '<?php echo admin_url('admin.php?page=wimp-create-invoice'); ?>&template_id=' + templateId;
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateCard = $(this).closest('.si-template-card');
        var templateName = templateCard.find('.si-template-title').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete the template', 'simple-invoice')); ?> "' + templateName + '"? <?php echo esc_js(__('This action cannot be undone.', 'simple-invoice')); ?>')) {
            deleteTemplate(templateId, templateCard);
        }
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateName = $(this).closest('.si-template-card').find('h3').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete', 'simple-invoice')); ?> "' + templateName + '"?')) {
            deleteTemplate(templateId);
        }
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeTemplateModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal-large', function(e) {
        if (e.target === this) {
            closeTemplateModal();
        }
    });

    // Prevent modal close when clicking inside content
    $('.si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });

    // Save template
    $('#si-save-template').on('click', function() {
        saveTemplate();
    });

    // Custom Fields Management
    var customFieldIndex = 0;
    var tableFieldIndex = 0;
    var summaryFieldIndex = 0;

    // Default table columns - All removable for flexibility
    var defaultTableColumns = [
        { name: 'sr_no', label: 'Sr. No', type: 'serial', required: false, removable: true },
        { name: 'description', label: 'Service/Product', type: 'text', required: false, removable: true },
        { name: 'quantity', label: 'Qty', type: 'number', required: false, removable: true },
        { name: 'rate', label: 'Rate', type: 'currency', required: false, removable: true },
        { name: 'total', label: 'Total', type: 'calculated', required: false, removable: true, formula: 'quantity * rate' }
    ];

    // Default summary fields - All removable for flexibility
    var defaultSummaryFields = [
        { name: 'subtotal', label: 'Subtotal', type: 'calculated', required: false, removable: true, formula: 'sum_total' },
        { name: 'tax_total', label: 'Tax Total', type: 'percentage', required: false, removable: true },
        { name: 'discount_total', label: 'Discount Total', type: 'currency', required: false, removable: true },
        { name: 'grand_total', label: 'Grand Total', type: 'calculated', required: false, removable: true, formula: 'subtotal + tax_total - discount_total' }
    ];



    // Add table field
    $('#si-add-table-field').on('click', function() {
        addTableField();
    });

    // Add summary field
    $('#si-add-summary-field').on('click', function() {
        addSummaryField();
    });

    // Remove custom field
    $(document).on('click', '.si-remove-field', function(e) {
        e.preventDefault();
        e.stopPropagation();
        var row = $(this).closest('.si-custom-field-row, .si-custom-table-field-row');
        if (row.hasClass('si-custom-field-row')) {
            row.remove();
            toggleCustomFieldsEmptyState();
        } else if (row.hasClass('si-custom-table-field-row')) {
            row.remove();
            toggleTableFieldsEmptyState();
        }
    });

    // Show/hide formula field based on field type
    $(document).on('change', '.si-field-type-select', function() {
        var fieldRow = $(this).closest('.si-custom-field-row, .si-custom-table-field-row');
        var formulaField = fieldRow.find('.si-formula-field');

        if ($(this).val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }
    });



    function addTableField(fieldData = {}) {
        var container = $('#si-table-columns-container');
        var field = {
            name: fieldData.name || 'custom_column_' + tableFieldIndex,
            label: fieldData.label || 'Custom Column',
            type: fieldData.type || 'text',
            required: fieldData.required || false,
            removable: true,
            formula: fieldData.formula || ''
        };

        var index = container.find('.si-sortable-field').length;
        addSortableField(container, field, 'table', index);
        tableFieldIndex++;
        updateFieldOrder(container);
    }





    // Auto-generate field name from label
    $(document).on('input', '.si-field-label', function() {
        var nameField = $(this).closest('.si-field-inputs').find('.si-field-name');
        if (!nameField.val()) {
            var generatedName = $(this).val().toLowerCase()
                .replace(/[^a-z\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+/, '')
                .replace(/_+/g, '_')
                .substring(0, 25);
            nameField.val(generatedName);
        }
    });

    // Validate field name input
    $(document).on('input', '.si-field-name', function() {
        var input = $(this);
        var value = input.val();
        var cleanValue = value.toLowerCase()
            .replace(/[^a-z_]/g, '')
            .replace(/^_+/, '')
            .replace(/_+/g, '_')
            .substring(0, 30);

        if (value !== cleanValue) {
            input.val(cleanValue);
        }
    });

    // Initialize default fields
    loadDefaultTableColumns();
    loadDefaultSummaryFields();

    // Initialize sortable
    initializeSortable();

    // Test function to verify remove buttons work
    window.testRemoveButtons = function() {
        console.log('Testing remove buttons...');
        $('.si-remove-field-btn').each(function(index) {
            console.log('Button ' + index + ':', $(this).length > 0 ? 'Found' : 'Not found');
        });
        console.log('Total remove buttons found:', $('.si-remove-field-btn').length);
    };

    // Call test function after a short delay
    setTimeout(function() {
        testRemoveButtons();
    }, 1000);

    function loadDefaultTableColumns() {
        var container = $('#si-table-columns-container');
        container.empty();

        defaultTableColumns.forEach(function(field, index) {
            addSortableField(container, field, 'table', index);
        });
    }

    function loadDefaultSummaryFields() {
        var container = $('#si-summary-fields-container');
        container.empty();

        defaultSummaryFields.forEach(function(field, index) {
            addSortableField(container, field, 'summary', index);
        });
    }

    function addSortableField(container, field, type, index) {
        var fieldHtml = `
            <div class="si-sortable-field" data-field-name="${field.name}" data-field-type="${type}" data-index="${index}">
                <input type="hidden" name="${type}_fields[${index}][order]" value="${index}" class="field-order" />

                <div class="si-field-label-cell">
                    <input type="text" name="${type}_fields[${index}][label]" value="${field.label}"
                           placeholder="e.g., Discount Amount" class="si-field-label-input" />
                </div>
                <div class="si-field-name-cell">
                    <input type="text" name="${type}_fields[${index}][name]" value="${field.name}"
                           placeholder="e.g., discount_amount" class="si-field-name-input"
                           pattern="[a-z_]+" title="Only lowercase letters and underscore" />
                </div>
                <div class="si-field-required-cell">
                    <input type="checkbox" name="${type}_fields[${index}][required]" value="1"
                           class="si-required-checkbox" ${field.required ? 'checked' : ''} />
                </div>
                <div class="si-field-type-cell">
                    <select name="${type}_fields[${index}][type]" class="si-field-type-select">
                        <option value="text" ${field.type === 'text' ? 'selected' : ''}>Text</option>
                        <option value="number" ${field.type === 'number' ? 'selected' : ''}>Number</option>
                        <option value="currency" ${field.type === 'currency' ? 'selected' : ''}>Currency</option>
                        <option value="percentage" ${field.type === 'percentage' ? 'selected' : ''}>Percentage</option>
                        <option value="calculated" ${field.type === 'calculated' ? 'selected' : ''}>Calculated</option>
                        <option value="serial" ${field.type === 'serial' ? 'selected' : ''}>Serial</option>
                    </select>
                    <div class="si-formula-field" style="${field.type === 'calculated' ? 'display:block;' : 'display:none;'}">
                        <input type="text" name="${type}_fields[${index}][formula]" value="${field.formula || ''}"
                               placeholder="e.g., quantity * rate" class="si-formula-input" />
                        <small>Available: quantity, rate, subtotal, tax, discount</small>
                    </div>
                </div>
                <div class="si-field-action-cell">
                    <span class="si-drag-handle dashicons dashicons-move" title="Drag to reorder"></span>
                    <button type="button" class="si-remove-field-btn" data-field="${field.name}">
                        <span class="dashicons dashicons-trash"></span>
                        Remove
                    </button>
                </div>
            </div>
        `;

        container.append(fieldHtml);

        // Immediately bind click handler to the new button
        container.find('.si-sortable-field:last .si-remove-field-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var button = $(this);
            var field = button.closest('.si-sortable-field');
            var container = field.parent();

            // Remove immediately without confirmation
            field.remove();
            updateFieldOrder(container);

            return false;
        });
    }

    function addSummaryField(fieldData = {}) {
        var container = $('#si-summary-fields-container');
        var field = {
            name: fieldData.name || 'custom_field_' + summaryFieldIndex,
            label: fieldData.label || 'Custom Field',
            type: fieldData.type || 'currency',
            required: fieldData.required || false,
            removable: true,
            formula: fieldData.formula || ''
        };

        var index = container.find('.si-sortable-field').length;
        addSortableField(container, field, 'summary', index);
        summaryFieldIndex++;
        updateFieldOrder(container);
    }

    function initializeSortable() {
        $('#si-table-columns-container, #si-summary-fields-container').sortable({
            handle: '.si-drag-handle',
            placeholder: 'si-sortable-field ui-sortable-placeholder',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            cancel: '.si-remove-field-btn, input, select',
            update: function(event, ui) {
                updateFieldOrder($(this));
            }
        });
    }

    function updateFieldOrder(container) {
        var type = container.attr('id').includes('table') ? 'table' : 'summary';

        container.find('.si-sortable-field').each(function(index) {
            var field = $(this);

            // Update all input names with new index
            field.find('input, select').each(function() {
                var input = $(this);
                var name = input.attr('name');
                if (name) {
                    // Replace the index in the name attribute
                    var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                    input.attr('name', newName);
                }
            });

            // Update data-index attribute
            field.attr('data-index', index);

            // Update order value
            field.find('.field-order').val(index);
        });
    }

    // Auto-generate field name from label for sortable fields
    $(document).on('input', '.si-field-label-input', function() {
        var labelInput = $(this);
        var nameInput = labelInput.closest('.si-sortable-field').find('.si-field-name-input');

        // Only auto-generate if name field is empty
        if (!nameInput.val().trim()) {
            var generatedName = labelInput.val().toLowerCase()
                .replace(/[^a-z\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+/, '')
                .replace(/_+/g, '_')
                .substring(0, 25);
            nameInput.val(generatedName);
        }
    });

    // Validate field name input for sortable fields
    $(document).on('input', '.si-field-name-input', function() {
        var input = $(this);
        var value = input.val();
        var cleanValue = value.toLowerCase()
            .replace(/[^a-z_]/g, '')
            .replace(/^_+/, '')
            .replace(/_+/g, '_')
            .substring(0, 30);

        if (value !== cleanValue) {
            input.val(cleanValue);
        }
    });

    // Show/hide formula field for sortable fields
    $(document).on('change', '.si-field-type-select', function() {
        var typeSelect = $(this);
        var formulaField = typeSelect.siblings('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }
    });

    // Simple and direct remove field handler
    $(document).on('click', '.si-remove-field-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var button = $(this);
        var field = button.closest('.si-sortable-field');
        var container = field.parent();

        if (field.length === 0) {
            return false;
        }

        // Remove immediately without confirmation
        field.remove();
        updateFieldOrder(container);

        return false;
    });

    function openTemplateModal(templateId) {
        isEditing = !!templateId;

        if (isEditing) {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Edit Invoice Type', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Update Invoice Type', 'simple-invoice')); ?>');
            loadTemplateData(templateId);
        } else {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Add New Invoice Type', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Save Invoice Type', 'simple-invoice')); ?>');
            templateForm[0].reset();
            $('#si-template-id').val('');
        }

        templateModal.show();
    }

    function closeTemplateModal() {
        templateModal.hide();
        templateForm[0].reset();
        isEditing = false;
    }

    function loadTemplateData(templateId) {

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_get_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data) {
                    var template = response.data;

                    // Fill basic information
                    $('#si-template-name').val(template.name || '');
                    $('#si-template-id').val(template.id || '');

                    // Set design selection
                    if (template.design) {
                        $('input[name="design"][value="' + template.design + '"]').prop('checked', true);
                    }

                    // Set header fields
                    if (template.header_fields) {
                        var headerFields = typeof template.header_fields === 'string' ?
                            JSON.parse(template.header_fields) : template.header_fields;

                        $.each(headerFields, function(field, value) {
                            $('input[name="header_fields[' + field + ']"]').prop('checked', !!value);
                        });
                    }

                    // Clear existing fields
                    $('#si-body-fields .si-field-row').remove();
                    $('#si-summary-fields .si-field-row').remove();

                    // Load body fields
                    if (template.body_fields) {
                        var bodyFields = typeof template.body_fields === 'string' ?
                            JSON.parse(template.body_fields) : template.body_fields;

                        if (Array.isArray(bodyFields)) {
                            bodyFields.forEach(function(field, index) {
                                addBodyField(field, index);
                            });
                        }
                    }

                    // Load summary fields
                    if (template.summary_fields) {
                        var summaryFields = typeof template.summary_fields === 'string' ?
                            JSON.parse(template.summary_fields) : template.summary_fields;

                        if (Array.isArray(summaryFields)) {
                            summaryFields.forEach(function(field, index) {
                                addSummaryField(field, index);
                            });
                        }
                    }

                    // Update available fields list
                    updateAvailableFieldsList();
                    toggleEmptyState();

                } else {
                    alert('<?php echo esc_js(__('Failed to load template data. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('Error loading template data. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function deleteTemplate(templateId, templateCard) {

        // Show loading state
        templateCard.css('opacity', '0.5');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Remove template card with animation
                    templateCard.fadeOut(300, function() {
                        $(this).remove();

                        // Check if no templates left
                        if ($('.si-template-card').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });

                    // Show success message (you can implement a toast notification here)
                    alert('<?php echo esc_js(__('Template deleted successfully.', 'simple-invoice')); ?>');

                } else {
                    templateCard.css('opacity', '1');
                    alert(response.data || '<?php echo esc_js(__('Failed to delete template. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                templateCard.css('opacity', '1');
                alert('<?php echo esc_js(__('Error deleting template. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function loadDefaultFields() {
        // Load default body fields
        var bodyFieldsContainer = $('#si-body-fields');
        bodyFieldsContainer.empty();
        
        defaultBodyFields.forEach(function(field, index) {
            addBodyField(field, index);
        });
        
        // Load default summary fields
        var summaryFieldsContainer = $('#si-summary-fields');
        summaryFieldsContainer.empty();
        
        defaultSummaryFields.forEach(function(field, index) {
            addSummaryField(field, index);
        });

        toggleEmptyState();

        // Ensure all formula fields are properly shown/hidden and remove buttons work
        setTimeout(function() {
            $('.si-field-type-select').each(function() {
                var fieldRow = $(this).closest('.si-field-row');
                var formulaField = fieldRow.find('.si-formula-field');

                if ($(this).val() === 'calculated') {
                    formulaField.show();
                } else {
                    formulaField.hide();
                }
            });

            // Ensure all remove buttons work
            $('.si-remove-field').each(function() {
                var button = $(this);
                button.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).closest('.si-field-row').remove();
                    updateAvailableFieldsList();
                    toggleEmptyState();
                });
            });
        }, 100);
    }
    
    function addBodyField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="body_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD, Product Name" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="body_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd, product_name" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="body_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="text"' + (field.type === 'text' ? ' selected' : '') + '>Text</option>' +
                    '<option value="number"' + (field.type === 'number' ? ' selected' : '') + '>Number</option>' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                    '<option value="serial"' + (field.type === 'serial' ? ' selected' : '') + '>Serial Number</option>' +
                '</select>' +
                '<label><input type="checkbox" name="body_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="body_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., quantity * rate, abcd + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">quantity, rate, subtotal</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-body-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-body-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    function addSummaryField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="summary_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD Total, Final Amount" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="summary_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd_total, final_amount" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="summary_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="percentage"' + (field.type === 'percentage' ? ' selected' : '') + '>Percentage</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                '</select>' +
                '<label><input type="checkbox" name="summary_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="summary_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., subtotal + tax - discount, abcd_total + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">subtotal, tax, discount, shipping</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-summary-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-summary-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    // Add new body field
    $('#si-add-body-field').on('click', function() {
        var index = $('#si-body-fields .si-field-row').length;
        addBodyField({}, index);
    });
    
    // Add new summary field
    $('#si-add-summary-field').on('click', function() {
        var index = $('#si-summary-fields .si-field-row').length;
        addSummaryField({}, index);
    });
    
    // Remove field
    $(document).on('click', '.si-remove-field', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var fieldRow = $(this).closest('.si-field-row');

        if (fieldRow.length > 0) {
            fieldRow.remove();

            updateAvailableFieldsList();
            toggleEmptyState();
        }
    });

    // Show/hide formula field based on field type
    $(document).on('change', '.si-field-type-select', function() {
        var fieldRow = $(this).closest('.si-field-row');
        var formulaField = fieldRow.find('.si-formula-field');
        var selectedValue = $(this).val();

        if (selectedValue === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }
    });

    // Update available fields list when field name changes
    $(document).on('input', '.si-field-name', function() {
        updateAvailableFieldsList();
    });

    // Additional remove button handler (backup)
    $(document).on('mousedown', '.si-remove-field', function(e) {
        e.preventDefault();
    });

    // Touch support for mobile
    $(document).on('touchstart', '.si-remove-field', function(e) {
        e.preventDefault();
        $(this).trigger('click');
    });

    // Validate formula on input
    $(document).on('input', '.si-formula-field input[type="text"]', function() {
        validateFormula($(this));
    });

    // Auto-generate field name from label
    $(document).on('input', '.si-field-label', function() {
        var nameField = $(this).closest('.si-field-inputs').find('.si-field-name');
        if (!nameField.val()) {
            var generatedName = $(this).val().toLowerCase()
                .replace(/[^a-z\s]/g, '')     // Only letters and spaces
                .replace(/\s+/g, '_')         // Replace spaces with underscore
                .replace(/^_+/, '')           // Remove leading underscores
                .replace(/_+/g, '_')          // Replace multiple underscores with single
                .substring(0, 25);            // Limit length
            nameField.val(generatedName);
            updateAvailableFieldsList();
        }
    });

    // Validate field name input - only lowercase letters and underscore
    $(document).on('input', '.si-field-name', function() {
        var input = $(this);
        var value = input.val();
        var cleanValue = value.toLowerCase()
            .replace(/[^a-z_]/g, '')  // Only allow lowercase letters and underscore
            .replace(/^_+/, '')       // Remove leading underscores
            .replace(/_+/g, '_')      // Replace multiple underscores with single
            .substring(0, 30);        // Limit length

        if (value !== cleanValue) {
            input.val(cleanValue);

            // Show validation message
            var existingMsg = input.siblings('.si-field-name-error');
            existingMsg.remove();

            if (value !== cleanValue) {
                input.after('<div class="si-field-name-error">Only lowercase letters and underscore allowed</div>');
                setTimeout(function() {
                    input.siblings('.si-field-name-error').fadeOut(function() {
                        $(this).remove();
                    });
                }, 2000);
            }
        }

        updateAvailableFieldsList();
    });

    // Prevent invalid characters on keypress
    $(document).on('keypress', '.si-field-name', function(e) {
        var char = String.fromCharCode(e.which);
        var allowedChars = /[a-z_]/;

        if (!allowedChars.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
            return false;
        }
    });

    // Prevent paste of invalid content
    $(document).on('paste', '.si-field-name', function(e) {
        var input = $(this);
        setTimeout(function() {
            var value = input.val();
            var cleanValue = value.toLowerCase()
                .replace(/[^a-z_]/g, '')
                .replace(/^_+/, '')
                .replace(/_+/g, '_')
                .substring(0, 30);
            input.val(cleanValue);
            updateAvailableFieldsList();
        }, 1);
    });

    // Function to update available fields list in formula descriptions
    function updateAvailableFieldsList() {
        var bodyFieldNames = [];
        var summaryFieldNames = [];

        // Collect body field names
        $('#si-body-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                bodyFieldNames.push(name);
            }
        });

        // Collect summary field names
        $('#si-summary-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                summaryFieldNames.push(name);
            }
        });

        // Add default field names
        var defaultBodyFields = ['quantity', 'rate', 'subtotal'];
        var defaultSummaryFields = ['subtotal', 'tax', 'discount', 'shipping'];

        var allBodyFields = defaultBodyFields.concat(bodyFieldNames);
        var allSummaryFields = defaultSummaryFields.concat(summaryFieldNames).concat(bodyFieldNames);

        // Update body fields available list
        $('#si-body-fields .si-field-names-list').text(allBodyFields.join(', '));

        // Update summary fields available list
        $('#si-summary-fields .si-field-names-list').text(allSummaryFields.join(', '));
    }

    // Formula validation function
    function validateFormula(formulaInput) {
        var formula = formulaInput.val().trim();
        var errorContainer = formulaInput.siblings('.si-formula-error');
        var successContainer = formulaInput.siblings('.si-formula-success');

        // Remove existing validation messages
        errorContainer.remove();
        successContainer.remove();

        if (!formula) {
            return;
        }

        // Get available field names
        var availableFields = [];
        formulaInput.closest('.si-field-row').parent().find('.si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                availableFields.push(name);
            }
        });

        // Add default fields
        if (formulaInput.closest('#si-body-fields').length) {
            availableFields = availableFields.concat(['quantity', 'rate', 'subtotal']);
        } else {
            availableFields = availableFields.concat(['subtotal', 'tax', 'discount', 'shipping']);
        }

        // Basic formula validation
        var isValid = true;
        var errorMessage = '';

        // Check for basic syntax
        if (!/^[a-zA-Z0-9_\s\+\-\*\/\(\)\.]+$/.test(formula)) {
            isValid = false;
            errorMessage = 'Formula contains invalid characters. Use only letters, numbers, +, -, *, /, (, )';
        }

        // Check for field names
        var fieldNames = formula.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
        for (var i = 0; i < fieldNames.length; i++) {
            if (availableFields.indexOf(fieldNames[i]) === -1 &&
                !['sum_total'].includes(fieldNames[i])) {
                isValid = false;
                errorMessage = 'Unknown field: "' + fieldNames[i] + '". Available fields: ' + availableFields.join(', ');
                break;
            }
        }

        // Show validation result
        if (isValid) {
            formulaInput.after('<div class="si-formula-success">✓ Formula looks good!</div>');
        } else {
            formulaInput.after('<div class="si-formula-error">✗ ' + errorMessage + '</div>');
        }
    }

    // Toggle empty state visibility
    function toggleEmptyState() {
        // Body fields empty state
        var bodyFieldsCount = $('#si-body-fields .si-field-row').length;
        if (bodyFieldsCount === 0) {
            $('#si-body-fields-empty').show();
        } else {
            $('#si-body-fields-empty').hide();
        }

        // Summary fields empty state
        var summaryFieldsCount = $('#si-summary-fields .si-field-row').length;
        if (summaryFieldsCount === 0) {
            $('#si-summary-fields-empty').show();
        } else {
            $('#si-summary-fields-empty').hide();
        }
    }






    
    function saveTemplate() {
        var formData = templateForm.serialize();
        var action = isEditing ? 'si_edit_template' : 'si_add_template';
        var nonce = isEditing ? '<?php echo wp_create_nonce('si_edit_template_nonce'); ?>' : '<?php echo wp_create_nonce('si_add_template_nonce'); ?>';
        
        formData += '&action=' + action + '&nonce=' + nonce;
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    closeTemplateModal();
                    location.reload(); // Refresh the page to show updated data
                } else {
                    alert(response.message || '<?php echo esc_js(__('An error occurred.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function deleteTemplate(templateId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('.si-template-card[data-template-id="' + templateId + '"]').fadeOut(function() {
                        $(this).remove();
                        
                        // Check if grid is empty
                        if ($('.si-template-card').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to delete template.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    // Initialize default fields on page load
    loadDefaultFields();

    // Design Guide Modal
    $('.si-view-guide').on('click', function(e) {
        e.preventDefault();
        $('#si-design-guide-modal').fadeIn(300);
    });

    // Close design guide modal
    $('#si-design-guide-modal .si-modal-close, #si-design-guide-modal').on('click', function(e) {
        if (e.target === this) {
            $('#si-design-guide-modal').fadeOut(300);
        }
    });

    // Prevent modal content clicks from closing modal
    $('#si-design-guide-modal .si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });
});
</script>

<!-- Design Guide Modal -->
<div id="si-design-guide-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content si-modal-large">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Custom Design Guide', 'simple-invoice'); ?></h2>
            <span class="si-modal-close">&times;</span>
        </div>
        <div class="si-modal-body">
            <div class="si-guide-content">
                <h3><?php echo esc_html__('How to Add Custom Designs', 'simple-invoice'); ?></h3>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">1</span> <?php echo esc_html__('Create Design Folder', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Create a new folder in:', 'simple-invoice'); ?></p>
                    <code>wp-content/plugins/simple-invoice/designs/your-design-name/</code>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">2</span> <?php echo esc_html__('Required Files', 'simple-invoice'); ?></h4>
                    <ul>
                        <li><strong>template.php</strong> - <?php echo esc_html__('Main template file (required)', 'simple-invoice'); ?></li>
                        <li><strong>style.css</strong> - <?php echo esc_html__('Custom CSS styles (optional)', 'simple-invoice'); ?></li>
                        <li><strong>preview.jpg</strong> - <?php echo esc_html__('Preview image (optional)', 'simple-invoice'); ?></li>
                    </ul>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">3</span> <?php echo esc_html__('Available Variables', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Use these variables in your template:', 'simple-invoice'); ?></p>
                    <div class="si-variables-grid">
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Business Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$settings['business_name']</code></li>
                                <li><code>$settings['business_address']</code></li>
                                <li><code>$settings['business_email']</code></li>
                                <li><code>$settings['business_phone']</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$invoice_number</code></li>
                                <li><code>$invoice_date</code></li>
                                <li><code>$due_date</code></li>
                                <li><code>$notes</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Client Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$client->name</code></li>
                                <li><code>$client->email</code></li>
                                <li><code>$client->address</code></li>
                                <li><code>$client->contact_number</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Calculations', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$items</code> - <?php echo esc_html__('Array of items', 'simple-invoice'); ?></li>
                                <li><code>$subtotal</code></li>
                                <li><code>$tax_amount</code></li>
                                <li><code>$total_amount</code></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">4</span> <?php echo esc_html__('Quick Start', 'simple-invoice'); ?></h4>
                    <ol>
                        <li><?php echo esc_html__('Copy an existing design folder (e.g., "classic")', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Rename it to your design name', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Modify template.php with your custom HTML/CSS', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Refresh this page to see your new design', 'simple-invoice'); ?></li>
                    </ol>
                </div>

                <div class="si-guide-actions">
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'DESIGN-TEMPLATES-GUIDE.md'); ?>" target="_blank" class="si-btn si-btn-primary">
                        <span class="dashicons dashicons-external"></span>
                        <?php echo esc_html__('View Full Documentation', 'simple-invoice'); ?>
                    </a>
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'designs/classic/template.php'); ?>" target="_blank" class="si-btn si-btn-secondary">
                        <span class="dashicons dashicons-media-code"></span>
                        <?php echo esc_html__('View Example Template', 'simple-invoice'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
